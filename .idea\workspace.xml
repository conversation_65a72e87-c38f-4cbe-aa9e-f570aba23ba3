<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9da6fcbb-5392-46ba-abc0-f149214456fb" name="Changes" comment="删除对话缓存">
      <change beforePath="$PROJECT_DIR$/flood_control/parameter/input.json" beforeDir="false" afterPath="$PROJECT_DIR$/flood_control/parameter/input.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/flood_control/sanxia_scheduling.py" beforeDir="false" afterPath="$PROJECT_DIR$/flood_control/sanxia_scheduling.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/requirements.txt" beforeDir="false" afterPath="$PROJECT_DIR$/requirements.txt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PREVIEW_PUSH_PROTECTED_ONLY" value="true" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2zcXeqBKN19PVtknzFYkHisoOaE" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.Agent_Chat.executor": "Run",
    "Python.api.executor": "Run",
    "Python.create_react_agent.executor": "Run",
    "Python.example_agent_with_sch_mcp.executor": "Run",
    "Python.graph.executor": "Run",
    "Python.import_water_data.executor": "Run",
    "Python.lightrag_openai_compatible_demo.executor": "Run",
    "Python.main.executor": "Run",
    "Python.rag (1).executor": "Run",
    "Python.run_import.executor": "Run",
    "Python.sanxia_scheduling.executor": "Run",
    "Python.sanxia_scheduling_base.executor": "Run",
    "Python.test.executor": "Run",
    "Python.test_sch_mcp.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "G:/Study/Python/Workflow",
    "settings.editor.selected.configurable": "project.propVCSSupport.Confirmation"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="G:\Study\Python\Workflow" />
      <recent name="G:\Study\Python\Workflow\LightRAG-1.4.3\examples\unofficial-sample" />
      <recent name="G:\Study\Python\Workflow\Langgraph_Learning" />
      <recent name="G:\Study\Python\Workflow\Langgraph_Learning\data_agent" />
      <recent name="G:\Study\Python\Workflow\flood_control" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="G:\Study\Python\Workflow\LightRAG-1.4.3\examples" />
      <recent name="G:\Study\Python\Workflow" />
      <recent name="G:\Study\Python\Workflow\Langgraph_Learning\MCP" />
      <recent name="G:\Study\Python\Workflow\Langgraph_Learning\data_agent\import_data" />
      <recent name="G:\Study\Python\Workflow\Langgraph_Learning\chatbot" />
    </key>
  </component>
  <component name="RunManager" selected="Python.sanxia_scheduling">
    <configuration name="lightrag_openai_compatible_demo" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Workflow" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/LightRAG-1.4.3/examples" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/LightRAG-1.4.3/examples/lightrag_openai_compatible_demo.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="rag (1)" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Workflow" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/LightRAG-1.4.3/examples" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/LightRAG-1.4.3/examples/rag.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="rag" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Workflow" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/LightRAG-1.4.3/examples" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="G:\Study\Python\Workflow\LightRAG-1.4.3\examples\rag.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="sanxia_scheduling" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Workflow" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/flood_control" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/flood_control/sanxia_scheduling.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_sch_mcp" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Workflow" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Langgraph_Learning/MCP" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/Langgraph_Learning/MCP/test_sch_mcp.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.sanxia_scheduling" />
        <item itemvalue="Python.rag (1)" />
        <item itemvalue="Python.rag" />
        <item itemvalue="Python.lightrag_openai_compatible_demo" />
        <item itemvalue="Python.test_sch_mcp" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="9da6fcbb-5392-46ba-abc0-f149214456fb" name="Changes" comment="" />
      <created>1752031432159</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752031432159</updated>
      <workItem from="1752031433907" duration="727000" />
      <workItem from="1752053524584" duration="2360000" />
      <workItem from="1752114763355" duration="7708000" />
      <workItem from="1752208716608" duration="6145000" />
      <workItem from="1752452376372" duration="3913000" />
      <workItem from="1752457925975" duration="966000" />
      <workItem from="1752458901160" duration="8000" />
      <workItem from="1752458915363" duration="9804000" />
      <workItem from="1752474843305" duration="4227000" />
      <workItem from="1752479092797" duration="486000" />
      <workItem from="1752479588797" duration="6974000" />
      <workItem from="1752538482202" duration="19689000" />
      <workItem from="1752567315851" duration="1407000" />
      <workItem from="1752568821835" duration="3548000" />
      <workItem from="1752624796982" duration="44000" />
      <workItem from="1752624877618" duration="26709000" />
      <workItem from="1752711594382" duration="14999000" />
      <workItem from="1752812472869" duration="2039000" />
      <workItem from="1753057039299" duration="6794000" />
      <workItem from="1753065710907" duration="17523000" />
      <workItem from="1753090385544" duration="1505000" />
      <workItem from="1753096883940" duration="117000" />
      <workItem from="1753143155593" duration="70000" />
      <workItem from="1753143325856" duration="188000" />
      <workItem from="1753143537436" duration="26951000" />
      <workItem from="1753229438267" duration="14432000" />
      <workItem from="1753261214933" duration="5105000" />
      <workItem from="1753316405574" duration="17286000" />
      <workItem from="1753345160187" duration="3554000" />
      <workItem from="1753402386869" duration="16038000" />
      <workItem from="1753661812549" duration="15455000" />
    </task>
    <task id="LOCAL-00001" summary="data_agent第一版">
      <option name="closed" value="true" />
      <created>1752735487527</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752735487527</updated>
    </task>
    <task id="LOCAL-00002" summary="MCP-learning">
      <option name="closed" value="true" />
      <created>1753082495404</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753082495404</updated>
    </task>
    <task id="LOCAL-00003" summary="MCP-第一版">
      <option name="closed" value="true" />
      <created>1753172167789</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753172167789</updated>
    </task>
    <task id="LOCAL-00004" summary="mysql通用添加数据">
      <option name="closed" value="true" />
      <created>1753251978059</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753251978059</updated>
    </task>
    <task id="LOCAL-00005" summary="调度大模型第一版">
      <option name="closed" value="true" />
      <created>1753269423679</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753269423679</updated>
    </task>
    <task id="LOCAL-00006" summary="LightRag继承">
      <option name="closed" value="true" />
      <created>1753349191149</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753349191149</updated>
    </task>
    <task id="LOCAL-00007" summary="删除对话缓存">
      <option name="closed" value="true" />
      <created>1753662913507</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753662913507</updated>
    </task>
    <option name="localTasksCounter" value="8" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="data_agent第一版" />
    <MESSAGE value="MCP-learning" />
    <MESSAGE value="MCP-第一版" />
    <MESSAGE value="mysql通用添加数据" />
    <MESSAGE value="调度大模型第一版" />
    <MESSAGE value="LightRag继承" />
    <MESSAGE value="删除对话缓存" />
    <option name="LAST_COMMIT_MESSAGE" value="删除对话缓存" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/Workflow$Agent_Chat.coverage" NAME="Agent_Chat Coverage Results" MODIFIED="1752217655022" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Langgraph_Learning" />
    <SUITE FILE_PATH="coverage/Workflow$sanxia_scheduling.coverage" NAME="sanxia_scheduling Coverage Results" MODIFIED="1753667293734" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/flood_control" />
    <SUITE FILE_PATH="coverage/Workflow$create_react_agent.coverage" NAME="create_react_agent Coverage Results" MODIFIED="1752566140751" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Langgraph_Learning" />
    <SUITE FILE_PATH="coverage/Workflow$sanxia_scheduling_base.coverage" NAME="sanxia_scheduling_base Coverage Results" MODIFIED="1752544354785" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/flood_control" />
    <SUITE FILE_PATH="coverage/Workflow$run_import.coverage" NAME="run_import Coverage Results" MODIFIED="1752732602671" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Langgraph_Learning/data_agent/import_data" />
    <SUITE FILE_PATH="coverage/Workflow$lightrag_openai_compatible_demo.coverage" NAME="lightrag_openai_compatible_demo Coverage Results" MODIFIED="1753326293595" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/LightRAG-1.4.3/examples" />
    <SUITE FILE_PATH="coverage/Workflow$test.coverage" NAME="test Coverage Results" MODIFIED="1753331578065" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/LightRAG-1.4.3/examples" />
    <SUITE FILE_PATH="coverage/Workflow$rag__1_.coverage" NAME="rag (1) Coverage Results" MODIFIED="1753420104366" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/LightRAG-1.4.3/examples" />
    <SUITE FILE_PATH="coverage/Workflow$test_sch_mcp.coverage" NAME="test_sch_mcp Coverage Results" MODIFIED="1753251286095" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Langgraph_Learning/MCP" />
    <SUITE FILE_PATH="coverage/Workflow$main.coverage" NAME="main Coverage Results" MODIFIED="1753250706468" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Langgraph_Learning/data_agent/import_data" />
    <SUITE FILE_PATH="coverage/Workflow$api.coverage" NAME="api Coverage Results" MODIFIED="1752131147982" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/MinerU" />
    <SUITE FILE_PATH="coverage/Workflow$example_agent_with_sch_mcp.coverage" NAME="example_agent_with_sch_mcp Coverage Results" MODIFIED="1753175123342" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Langgraph_Learning/MCP" />
    <SUITE FILE_PATH="coverage/Workflow$import_water_data.coverage" NAME="import_water_data Coverage Results" MODIFIED="1752732654220" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Langgraph_Learning/data_agent/import_data" />
    <SUITE FILE_PATH="coverage/Workflow$graph.coverage" NAME="graph Coverage Results" MODIFIED="1753086797978" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Langgraph_Learning/data_agent" />
  </component>
</project>